<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - OllamaServiceTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>OllamaServiceTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.aicodingcli.ai.providers.html">com.aicodingcli.ai.providers</a> &gt; OllamaServiceTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">8</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.025s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">should fail connection test on error()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle API error response()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle streaming chat request()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle temperature and max tokens options()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should make successful chat request()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should test connection successfully()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should use custom base URL if provided()</td>
<td class="success">0.007s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should validate request before sending()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>18:38:33.131 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#15
18:38:33.134 [Test worker @kotlinx.coroutines.test runner#62] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #15#16
18:38:33.136 [Test worker @kotlinx.coroutines.test runner#62] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    &quot;model&quot;: &quot;llama2&quot;,
    &quot;created_at&quot;: &quot;2023-12-12T14:13:43.416799Z&quot;,
    &quot;message&quot;: {
        &quot;role&quot;: &quot;assistant&quot;,
        &quot;content&quot;: &quot;Hi&quot;
    },
    &quot;done&quot;: true,
    &quot;total_duration&quot;: 1000000,
    &quot;eval_count&quot;: 1,
    &quot;prompt_eval_count&quot;: 1
}, headers={}) on <EMAIL>(http://custom-ollama:11434/api/chat, {&quot;model&quot;:&quot;llama2&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Hello&quot;}],&quot;stream&quot;:false,&quot;options&quot;:{&quot;temperature&quot;:0.7,&quot;num_predict&quot;:1000}}, {Content-Type=application/json}, continuation {})
18:38:33.139 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#17
18:38:33.141 [Test worker @kotlinx.coroutines.test runner#68] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #17#18
18:38:33.141 [Test worker @kotlinx.coroutines.test runner#68] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    &quot;model&quot;: &quot;llama2&quot;,
    &quot;created_at&quot;: &quot;2023-12-12T14:13:43.416799Z&quot;,
    &quot;message&quot;: {
        &quot;role&quot;: &quot;assistant&quot;,
        &quot;content&quot;: &quot;Hello there!&quot;
    },
    &quot;done&quot;: true,
    &quot;eval_count&quot;: 2,
    &quot;prompt_eval_count&quot;: 1
}, headers={}) on <EMAIL>(http://localhost:11434/api/chat, {&quot;model&quot;:&quot;llama2&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Hello&quot;}],&quot;stream&quot;:false,&quot;options&quot;:{&quot;temperature&quot;:0.5,&quot;num_predict&quot;:500}}, {Content-Type=application/json}, continuation {})
18:38:33.144 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#19
18:38:33.145 [Test worker @kotlinx.coroutines.test runner#74] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #19#20
18:38:33.145 [Test worker @kotlinx.coroutines.test runner#74] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    &quot;models&quot;: [
        {
            &quot;name&quot;: &quot;llama2:latest&quot;,
            &quot;model&quot;: &quot;llama2:latest&quot;,
            &quot;size&quot;: 3825819519,
            &quot;digest&quot;: &quot;fe938a131f40e6f6d40083c9f0f430a515233eb2edaa6d72eb85c50d64f2300e&quot;,
            &quot;details&quot;: {
                &quot;format&quot;: &quot;gguf&quot;,
                &quot;family&quot;: &quot;llama&quot;,
                &quot;parameter_size&quot;: &quot;7B&quot;,
                &quot;quantization_level&quot;: &quot;Q4_0&quot;
            }
        }
    ]
}, headers={Content-Type=application/json}) on <EMAIL>(http://localhost:11434/api/tags, {Content-Type=application/json}, continuation {})
18:38:33.147 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#21
18:38:33.149 [Test worker @kotlinx.coroutines.test runner#78] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #21#22
18:38:33.149 [Test worker @kotlinx.coroutines.test runner#78] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 503: Service <NAME_EMAIL>(http://localhost:11434/api/tags, {Content-Type=application/json}, continuation {})
18:38:33.150 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#23
18:38:33.151 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#24
18:38:33.153 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#25
18:38:33.155 [Test worker @kotlinx.coroutines.test runner#86] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #25#26
18:38:33.155 [Test worker @kotlinx.coroutines.test runner#86] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    &quot;model&quot;: &quot;llama2&quot;,
    &quot;created_at&quot;: &quot;2023-12-12T14:13:43.416799Z&quot;,
    &quot;message&quot;: {
        &quot;role&quot;: &quot;assistant&quot;,
        &quot;content&quot;: &quot;Hello! How can I help you today?&quot;
    },
    &quot;done&quot;: true,
    &quot;total_duration&quot;: 5191566416,
    &quot;load_duration&quot;: 2154458,
    &quot;prompt_eval_count&quot;: 26,
    &quot;prompt_eval_duration&quot;: 383809000,
    &quot;eval_count&quot;: 298,
    &quot;eval_duration&quot;: 4799921000
}, headers={Content-Type=application/json}) on <EMAIL>(http://localhost:11434/api/chat, {&quot;model&quot;:&quot;llama2&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Hello, Ollama!&quot;}],&quot;stream&quot;:false,&quot;options&quot;:{&quot;temperature&quot;:0.7,&quot;num_predict&quot;:1000}}, {Content-Type=application/json}, continuation {})
18:38:33.157 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#27
18:38:33.159 [Test worker @kotlinx.coroutines.test runner#92] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #27#28
18:38:33.159 [Test worker @kotlinx.coroutines.test runner#92] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 404: Not <NAME_EMAIL>(http://localhost:11434/api/chat, {&quot;model&quot;:&quot;llama2&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Hello&quot;}],&quot;stream&quot;:false,&quot;options&quot;:{&quot;temperature&quot;:0.7,&quot;num_predict&quot;:1000}}, {Content-Type=application/json}, continuation {})
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月17日 18:38:33</p>
</div>
</div>
</body>
</html>
