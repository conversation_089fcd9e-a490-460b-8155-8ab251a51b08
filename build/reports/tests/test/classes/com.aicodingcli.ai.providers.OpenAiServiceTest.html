<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - OpenAiServiceTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>OpenAiServiceTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.aicodingcli.ai.providers.html">com.aicodingcli.ai.providers</a> &gt; OpenAiServiceTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">7</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.023s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">should fail connection test on error()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle API error response()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle streaming chat request()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should make successful chat request()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should test connection successfully()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should use custom base URL if provided()</td>
<td class="success">0.009s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should validate request before sending()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>17:09:30.397 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#15
17:09:30.398 [Test worker @kotlinx.coroutines.test runner#48] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #15#16
17:09:30.402 [Test worker @kotlinx.coroutines.test runner#48] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    &quot;id&quot;: &quot;chatcmpl-test&quot;,
    &quot;object&quot;: &quot;chat.completion&quot;,
    &quot;created&quot;: 1677652288,
    &quot;model&quot;: &quot;gpt-3.5-turbo&quot;,
    &quot;choices&quot;: [
        {
            &quot;index&quot;: 0,
            &quot;message&quot;: {
                &quot;role&quot;: &quot;assistant&quot;,
                &quot;content&quot;: &quot;Hi&quot;
            },
            &quot;finish_reason&quot;: &quot;stop&quot;
        }
    ],
    &quot;usage&quot;: {
        &quot;prompt_tokens&quot;: 1,
        &quot;completion_tokens&quot;: 1,
        &quot;total_tokens&quot;: 2
    }
}, headers={}) on <EMAIL>(https://custom.openai.com/v1/chat/completions, {&quot;model&quot;:&quot;gpt-3.5-turbo&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Hello&quot;}],&quot;temperature&quot;:0.7,&quot;max_tokens&quot;:1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
17:09:30.406 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#17
17:09:30.407 [Test worker @kotlinx.coroutines.test runner#54] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #17#18
17:09:30.407 [Test worker @kotlinx.coroutines.test runner#54] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={&quot;object&quot;: &quot;list&quot;, &quot;data&quot;: [{&quot;id&quot;: &quot;gpt-3.5-turbo&quot;, &quot;object&quot;: &quot;model&quot;, &quot;created&quot;: 1677610602, &quot;owned_by&quot;: &quot;openai&quot;}]}, headers={Content-Type=application/json}) on <EMAIL>(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
17:09:30.411 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#19
17:09:30.412 [Test worker @kotlinx.coroutines.test runner#58] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #19#20
17:09:30.412 [Test worker @kotlinx.coroutines.test runner#58] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
17:09:30.413 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#21
17:09:30.414 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#22
17:09:30.416 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#23
17:09:30.417 [Test worker @kotlinx.coroutines.test runner#66] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #23#24
17:09:30.417 [Test worker @kotlinx.coroutines.test runner#66] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    &quot;id&quot;: &quot;chatcmpl-123&quot;,
    &quot;object&quot;: &quot;chat.completion&quot;,
    &quot;created&quot;: 1677652288,
    &quot;model&quot;: &quot;gpt-3.5-turbo&quot;,
    &quot;choices&quot;: [
        {
            &quot;index&quot;: 0,
            &quot;message&quot;: {
                &quot;role&quot;: &quot;assistant&quot;,
                &quot;content&quot;: &quot;Hello! How can I help you today?&quot;
            },
            &quot;finish_reason&quot;: &quot;stop&quot;
        }
    ],
    &quot;usage&quot;: {
        &quot;prompt_tokens&quot;: 9,
        &quot;completion_tokens&quot;: 12,
        &quot;total_tokens&quot;: 21
    }
}, headers={Content-Type=application/json}) on <EMAIL>(https://api.openai.com/v1/chat/completions, {&quot;model&quot;:&quot;gpt-3.5-turbo&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Hello, AI!&quot;}],&quot;temperature&quot;:0.7,&quot;max_tokens&quot;:1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
17:09:30.419 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#25
17:09:30.420 [Test worker @kotlinx.coroutines.test runner#72] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #25#26
17:09:30.420 [Test worker @kotlinx.coroutines.test runner#72] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.openai.com/v1/chat/completions, {&quot;model&quot;:&quot;gpt-3.5-turbo&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Hello&quot;}],&quot;temperature&quot;:0.7,&quot;max_tokens&quot;:1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月17日 17:09:30</p>
</div>
</div>
</body>
</html>
