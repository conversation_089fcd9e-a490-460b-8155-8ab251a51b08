<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">50</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">1.251s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Packages</a>
</li>
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.html">com.aicodingcli</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.223s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.ai.html">com.aicodingcli.ai</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.419s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.ai.providers.html">com.aicodingcli.ai.providers</a>
</td>
<td>22</td>
<td>0</td>
<td>0</td>
<td>0.125s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.config.html">com.aicodingcli.config</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.015s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.http.html">com.aicodingcli.http</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.469s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.AiCodingCliTest.html">com.aicodingcli.AiCodingCliTest</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.223s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.ai.AiRequestResponseTest.html">com.aicodingcli.ai.AiRequestResponseTest</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.414s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.ai.AiServiceTest.html">com.aicodingcli.ai.AiServiceTest</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>0.005s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.ai.providers.ClaudeServiceTest.html">com.aicodingcli.ai.providers.ClaudeServiceTest</a>
</td>
<td>7</td>
<td>0</td>
<td>0</td>
<td>0.085s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.ai.providers.OllamaServiceTest.html">com.aicodingcli.ai.providers.OllamaServiceTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.025s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.ai.providers.OpenAiServiceTest.html">com.aicodingcli.ai.providers.OpenAiServiceTest</a>
</td>
<td>7</td>
<td>0</td>
<td>0</td>
<td>0.015s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.config.ConfigManagerTest.html">com.aicodingcli.config.ConfigManagerTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.015s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.http.HttpClientTest.html">com.aicodingcli.http.HttpClientTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.469s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月17日 18:38:33</p>
</div>
</div>
</body>
</html>
