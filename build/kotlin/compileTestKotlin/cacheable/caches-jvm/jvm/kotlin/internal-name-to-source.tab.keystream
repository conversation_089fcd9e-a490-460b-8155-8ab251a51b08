    c o m / a i c o d i n g c l i / A i C o d i n g C l i T e s t   ( c o m / a i c o d i n g c l i / a i / A i R e q u e s t R e s p o n s e T e s t   m c o m / a i c o d i n g c l i / a i / A i R e q u e s t R e s p o n s e T e s t $ s h o u l d   v a l i d a t e   m e s s a g e   c o n t e n t   i s   n o t   e m p t y $ $ i n l i n e d $ a s s e r t T h r o w s $ 1   K c o m / a i c o d i n g c l i / a i / A i R e q u e s t R e s p o n s e T e s t $ s h o u l d   h a n d l e   s t r e a m i n g   r e s p o n s e $ 1   M c o m / a i c o d i n g c l i / a i / A i R e q u e s t R e s p o n s e T e s t $ s h o u l d   h a n d l e   s t r e a m i n g   r e s p o n s e $ 1 $ 1   M c o m / a i c o d i n g c l i / a i / A i R e q u e s t R e s p o n s e T e s t $ s h o u l d   h a n d l e   s t r e a m i n g   r e s p o n s e $ 1 $ 2     c o m / a i c o d i n g c l i / a i / A i S e r v i c e T e s t   T c o m / a i c o d i n g c l i / a i / A i S e r v i c e T e s t $ s h o u l d   c r e a t e   A I   s e r v i c e   w i t h   v a l i d   c o n f i g u r a t i o n $ 1   M c o m / a i c o d i n g c l i / a i / A i S e r v i c e T e s t $ s h o u l d   t h r o w   e x c e p t i o n   f o r   i n v a l i d   A P I   k e y $ 1   s c o m / a i c o d i n g c l i / a i / A i S e r v i c e T e s t $ s h o u l d   t h r o w   e x c e p t i o n   f o r   i n v a l i d   A P I   k e y $ 1 $ i n v o k e S u s p e n d $ $ i n l i n e d $ a s s e r t T h r o w s $ 1   G c o m / a i c o d i n g c l i / a i / A i S e r v i c e T e s t $ s h o u l d   s u p p o r t   m u l t i p l e   A I   p r o v i d e r s $ 1   C c o m / a i c o d i n g c l i / a i / A i S e r v i c e T e s t $ s h o u l d   v a l i d a t e   m o d e l   p a r a m e t e r s $ 1   i c o m / a i c o d i n g c l i / a i / A i S e r v i c e T e s t $ s h o u l d   v a l i d a t e   m o d e l   p a r a m e t e r s $ 1 $ i n v o k e S u s p e n d $ $ i n l i n e d $ a s s e r t T h r o w s $ 1   . c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t   T c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   m a k e   s u c c e s s f u l   c h a t   r e q u e s t $ 1   V c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   m a k e   s u c c e s s f u l   c h a t   r e q u e s t $ 1 $ 1   V c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   m a k e   s u c c e s s f u l   c h a t   r e q u e s t $ 1 $ 2   Q c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   h a n d l e   A P I   e r r o r   r e s p o n s e $ 1   S c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   h a n d l e   A P I   e r r o r   r e s p o n s e $ 1 $ 1   w c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   h a n d l e   A P I   e r r o r   r e s p o n s e $ 1 $ i n v o k e S u s p e n d $ $ i n l i n e d $ a s s e r t T h r o w s $ 1   T c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   t e s t   c o n n e c t i o n   s u c c e s s f u l l y $ 1   V c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   t e s t   c o n n e c t i o n   s u c c e s s f u l l y $ 1 $ 1   U c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   f a i l   c o n n e c t i o n   t e s t   o n   e r r o r $ 1   W c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   f a i l   c o n n e c t i o n   t e s t   o n   e r r o r $ 1 $ 1   U c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   h a n d l e   s t r e a m i n g   c h a t   r e q u e s t $ 1   W c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   v a l i d a t e   r e q u e s t   b e f o r e   s e n d i n g $ 1   } c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   v a l i d a t e   r e q u e s t   b e f o r e   s e n d i n g $ 1 $ i n v o k e S u s p e n d $ $ i n l i n e d $ a s s e r t T h r o w s $ 1   W c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   u s e   c u s t o m   b a s e   U R L   i f   p r o v i d e d $ 1   Y c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   u s e   c u s t o m   b a s e   U R L   i f   p r o v i d e d $ 1 $ 1   Y c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O p e n A i S e r v i c e T e s t $ s h o u l d   u s e   c u s t o m   b a s e   U R L   i f   p r o v i d e d $ 1 $ 2   ( c o m / a i c o d i n g c l i / c o n f i g / C o n f i g M a n a g e r T e s t   a c o m / a i c o d i n g c l i / c o n f i g / C o n f i g M a n a g e r T e s t $ s h o u l d   c r e a t e   d e f a u l t   c o n f i g u r a t i o n   f i l e   i f   n o t   e x i s t s $ 1   R c o m / a i c o d i n g c l i / c o n f i g / C o n f i g M a n a g e r T e s t $ s h o u l d   l o a d   e x i s t i n g   c o n f i g u r a t i o n   f i l e $ 1   L c o m / a i c o d i n g c l i / c o n f i g / C o n f i g M a n a g e r T e s t $ s h o u l d   s a v e   c o n f i g u r a t i o n   t o   f i l e $ 1   T c o m / a i c o d i n g c l i / c o n f i g / C o n f i g M a n a g e r T e s t $ s h o u l d   g e t   c u r r e n t   p r o v i d e r   c o n f i g u r a t i o n $ 1   O c o m / a i c o d i n g c l i / c o n f i g / C o n f i g M a n a g e r T e s t $ s h o u l d   u p d a t e   p r o v i d e r   c o n f i g u r a t i o n $ 1   I c o m / a i c o d i n g c l i / c o n f i g / C o n f i g M a n a g e r T e s t $ s h o u l d   s w i t c h   d e f a u l t   p r o v i d e r $ 1   # c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t   H c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   m a k e   s u c c e s s f u l   G E T   r e q u e s t $ 1   U c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   m a k e   s u c c e s s f u l   G E T   r e q u e s t $ 1 $ m o c k E n g i n e $ 1   X c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   m a k e   s u c c e s s f u l   P O S T   r e q u e s t   w i t h   J S O N   b o d y $ 1   e c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   m a k e   s u c c e s s f u l   P O S T   r e q u e s t   w i t h   J S O N   b o d y $ 1 $ m o c k E n g i n e $ 1   H c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   h a n d l e   H T T P   e r r o r   r e s p o n s e s $ 1   U c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   h a n d l e   H T T P   e r r o r   r e s p o n s e s $ 1 $ m o c k E n g i n e $ 1   n c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   h a n d l e   H T T P   e r r o r   r e s p o n s e s $ 1 $ i n v o k e S u s p e n d $ $ i n l i n e d $ a s s e r t T h r o w s $ 1   D c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   r e t r y   o n   n e t w o r k   e r r o r s $ 1   Q c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   r e t r y   o n   n e t w o r k   e r r o r s $ 1 $ m o c k E n g i n e $ 1   L c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   f a i l   a f t e r   m a x   r e t r i e s   e x c e e d e d $ 1   Y c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   f a i l   a f t e r   m a x   r e t r i e s   e x c e e d e d $ 1 $ m o c k E n g i n e $ 1   r c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   f a i l   a f t e r   m a x   r e t r i e s   e x c e e d e d $ 1 $ i n v o k e S u s p e n d $ $ i n l i n e d $ a s s e r t T h r o w s $ 1   ; c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   h a n d l e   t i m e o u t $ 1   H c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   h a n d l e   t i m e o u t $ 1 $ m o c k E n g i n e $ 1   a c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   h a n d l e   t i m e o u t $ 1 $ i n v o k e S u s p e n d $ $ i n l i n e d $ a s s e r t T h r o w s $ 1   ? c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   a d d   c u s t o m   h e a d e r s $ 1   L c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   a d d   c u s t o m   h e a d e r s $ 1 $ m o c k E n g i n e $ 1   R c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   h a n d l e   r a t e   l i m i t i n g   w i t h   r e t r y   a f t e r $ 1   _ c o m / a i c o d i n g c l i / h t t p / H t t p C l i e n t T e s t $ s h o u l d   h a n d l e   r a t e   l i m i t i n g   w i t h   r e t r y   a f t e r $ 1 $ m o c k E n g i n e $ 1   . c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t   T c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   m a k e   s u c c e s s f u l   c h a t   r e q u e s t $ 1   V c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   m a k e   s u c c e s s f u l   c h a t   r e q u e s t $ 1 $ 1   V c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   m a k e   s u c c e s s f u l   c h a t   r e q u e s t $ 1 $ 2   Q c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   h a n d l e   A P I   e r r o r   r e s p o n s e $ 1   S c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   h a n d l e   A P I   e r r o r   r e s p o n s e $ 1 $ 1   w c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   h a n d l e   A P I   e r r o r   r e s p o n s e $ 1 $ i n v o k e S u s p e n d $ $ i n l i n e d $ a s s e r t T h r o w s $ 1   T c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   t e s t   c o n n e c t i o n   s u c c e s s f u l l y $ 1   V c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   t e s t   c o n n e c t i o n   s u c c e s s f u l l y $ 1 $ 1   U c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   f a i l   c o n n e c t i o n   t e s t   o n   e r r o r $ 1   W c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   f a i l   c o n n e c t i o n   t e s t   o n   e r r o r $ 1 $ 1   U c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   h a n d l e   s t r e a m i n g   c h a t   r e q u e s t $ 1   W c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   v a l i d a t e   r e q u e s t   b e f o r e   s e n d i n g $ 1   } c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   v a l i d a t e   r e q u e s t   b e f o r e   s e n d i n g $ 1 $ i n v o k e S u s p e n d $ $ i n l i n e d $ a s s e r t T h r o w s $ 1   W c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   u s e   c u s t o m   b a s e   U R L   i f   p r o v i d e d $ 1   Y c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   u s e   c u s t o m   b a s e   U R L   i f   p r o v i d e d $ 1 $ 1   Y c o m / a i c o d i n g c l i / a i / p r o v i d e r s / C l a u d e S e r v i c e T e s t $ s h o u l d   u s e   c u s t o m   b a s e   U R L   i f   p r o v i d e d $ 1 $ 2   . c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t   T c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   m a k e   s u c c e s s f u l   c h a t   r e q u e s t $ 1   V c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   m a k e   s u c c e s s f u l   c h a t   r e q u e s t $ 1 $ 1   V c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   m a k e   s u c c e s s f u l   c h a t   r e q u e s t $ 1 $ 2   Q c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   h a n d l e   A P I   e r r o r   r e s p o n s e $ 1   S c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   h a n d l e   A P I   e r r o r   r e s p o n s e $ 1 $ 1   w c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   h a n d l e   A P I   e r r o r   r e s p o n s e $ 1 $ i n v o k e S u s p e n d $ $ i n l i n e d $ a s s e r t T h r o w s $ 1   T c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   t e s t   c o n n e c t i o n   s u c c e s s f u l l y $ 1   V c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   t e s t   c o n n e c t i o n   s u c c e s s f u l l y $ 1 $ 1   U c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   f a i l   c o n n e c t i o n   t e s t   o n   e r r o r $ 1   W c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   f a i l   c o n n e c t i o n   t e s t   o n   e r r o r $ 1 $ 1   U c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   h a n d l e   s t r e a m i n g   c h a t   r e q u e s t $ 1   W c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   v a l i d a t e   r e q u e s t   b e f o r e   s e n d i n g $ 1   } c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   v a l i d a t e   r e q u e s t   b e f o r e   s e n d i n g $ 1 $ i n v o k e S u s p e n d $ $ i n l i n e d $ a s s e r t T h r o w s $ 1   W c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   u s e   c u s t o m   b a s e   U R L   i f   p r o v i d e d $ 1   Y c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   u s e   c u s t o m   b a s e   U R L   i f   p r o v i d e d $ 1 $ 1   Y c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   u s e   c u s t o m   b a s e   U R L   i f   p r o v i d e d $ 1 $ 2   a c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   h a n d l e   t e m p e r a t u r e   a n d   m a x   t o k e n s   o p t i o n s $ 1   c c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   h a n d l e   t e m p e r a t u r e   a n d   m a x   t o k e n s   o p t i o n s $ 1 $ 1   c c o m / a i c o d i n g c l i / a i / p r o v i d e r s / O l l a m a S e r v i c e T e s t $ s h o u l d   h a n d l e   t e m p e r a t u r e   a n d   m a x   t o k e n s   o p t i o n s $ 1 $ 2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      