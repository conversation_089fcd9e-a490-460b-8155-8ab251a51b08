<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.http.HttpClientTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-06-17T09:09:30.444Z" hostname="zxnapdeMacBook-Pro.local" time="0.493">
  <properties/>
  <testcase name="should fail after max retries exceeded()" classname="com.aicodingcli.http.HttpClientTest" time="0.025"/>
  <testcase name="should make successful POST request with JSON body()" classname="com.aicodingcli.http.HttpClientTest" time="0.02"/>
  <testcase name="should add custom headers()" classname="com.aicodingcli.http.HttpClientTest" time="0.003"/>
  <testcase name="should make successful GET request()" classname="com.aicodingcli.http.HttpClientTest" time="0.003"/>
  <testcase name="should handle timeout()" classname="com.aicodingcli.http.HttpClientTest" time="0.429"/>
  <testcase name="should retry on network errors()" classname="com.aicodingcli.http.HttpClientTest" time="0.006"/>
  <testcase name="should handle HTTP error responses()" classname="com.aicodingcli.http.HttpClientTest" time="0.003"/>
  <testcase name="should handle rate limiting with retry after()" classname="com.aicodingcli.http.HttpClientTest" time="0.003"/>
  <system-out><![CDATA[17:09:30.460 [Test worker @kotlinx.coroutines.test runner#88] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
17:09:30.464 [Test worker @kotlinx.coroutines.test runner#88] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
17:09:30.466 [Test worker @kotlinx.coroutines.test runner#88] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
17:09:30.467 [Test worker @kotlinx.coroutines.test runner#88] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
17:09:30.468 [Test worker @kotlinx.coroutines.test runner#88] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
17:09:30.469 [Test worker @kotlinx.coroutines.test runner#88] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
17:09:30.474 [Test worker @kotlinx.coroutines.test runner#96] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/create
METHOD: HttpMethod(value=POST)
17:09:30.482 [Test worker @kotlinx.coroutines.test runner#96] INFO io.ktor.client.HttpClient -- RESPONSE: 201 Created
METHOD: HttpMethod(value=POST)
FROM: https://api.example.com/create
17:09:30.492 [Test worker @kotlinx.coroutines.test runner#100] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
17:09:30.492 [Test worker @kotlinx.coroutines.test runner#100] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
17:09:30.495 [Test worker @kotlinx.coroutines.test runner#104] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
17:09:30.495 [Test worker @kotlinx.coroutines.test runner#104] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
17:09:30.497 [Test worker @kotlinx.coroutines.test runner#108] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
17:09:30.600 [Test worker @kotlinx.coroutines.test runner#108] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
17:09:30.601 [Test worker @kotlinx.coroutines.test runner#108] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
17:09:30.709 [Test worker @kotlinx.coroutines.test runner#108] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
17:09:30.711 [Test worker @kotlinx.coroutines.test runner#108] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
17:09:30.814 [Test worker @kotlinx.coroutines.test runner#108] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
17:09:30.817 [Test worker @kotlinx.coroutines.test runner#108] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
17:09:30.921 [Test worker @kotlinx.coroutines.test runner#108] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
17:09:30.928 [Test worker @kotlinx.coroutines.test runner#118] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
17:09:30.929 [Test worker @kotlinx.coroutines.test runner#118] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Network error
17:09:30.930 [Test worker @kotlinx.coroutines.test runner#118] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
17:09:30.930 [Test worker @kotlinx.coroutines.test runner#118] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Network error
17:09:30.931 [Test worker @kotlinx.coroutines.test runner#118] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
17:09:30.931 [Test worker @kotlinx.coroutines.test runner#118] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
17:09:30.933 [Test worker @kotlinx.coroutines.test runner#126] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/notfound
METHOD: HttpMethod(value=GET)
17:09:30.934 [Test worker @kotlinx.coroutines.test runner#126] INFO io.ktor.client.HttpClient -- RESPONSE: 404 Not Found
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/notfound
17:09:30.937 [Test worker @kotlinx.coroutines.test runner#130] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
17:09:30.937 [Test worker @kotlinx.coroutines.test runner#130] INFO io.ktor.client.HttpClient -- RESPONSE: 429 Too Many Requests
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
17:09:30.938 [Test worker @kotlinx.coroutines.test runner#130] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
17:09:30.938 [Test worker @kotlinx.coroutines.test runner#130] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
