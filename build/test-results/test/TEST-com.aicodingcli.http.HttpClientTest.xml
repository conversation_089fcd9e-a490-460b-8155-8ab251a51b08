<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.http.HttpClientTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-06-17T10:25:42.714Z" hostname="zxnapdeMacBook-Pro.local" time="0.51">
  <properties/>
  <testcase name="should fail after max retries exceeded()" classname="com.aicodingcli.http.HttpClientTest" time="0.026"/>
  <testcase name="should make successful POST request with JSON body()" classname="com.aicodingcli.http.HttpClientTest" time="0.019"/>
  <testcase name="should add custom headers()" classname="com.aicodingcli.http.HttpClientTest" time="0.004"/>
  <testcase name="should make successful GET request()" classname="com.aicodingcli.http.HttpClientTest" time="0.002"/>
  <testcase name="should handle timeout()" classname="com.aicodingcli.http.HttpClientTest" time="0.435"/>
  <testcase name="should retry on network errors()" classname="com.aicodingcli.http.HttpClientTest" time="0.01"/>
  <testcase name="should handle HTTP error responses()" classname="com.aicodingcli.http.HttpClientTest" time="0.005"/>
  <testcase name="should handle rate limiting with retry after()" classname="com.aicodingcli.http.HttpClientTest" time="0.006"/>
  <system-out><![CDATA[18:25:42.731 [Test worker @kotlinx.coroutines.test runner#122] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
18:25:42.735 [Test worker @kotlinx.coroutines.test runner#122] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
18:25:42.737 [Test worker @kotlinx.coroutines.test runner#122] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
18:25:42.738 [Test worker @kotlinx.coroutines.test runner#122] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
18:25:42.739 [Test worker @kotlinx.coroutines.test runner#122] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
18:25:42.740 [Test worker @kotlinx.coroutines.test runner#122] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
18:25:42.744 [Test worker @kotlinx.coroutines.test runner#130] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/create
METHOD: HttpMethod(value=POST)
18:25:42.753 [Test worker @kotlinx.coroutines.test runner#130] INFO io.ktor.client.HttpClient -- RESPONSE: 201 Created
METHOD: HttpMethod(value=POST)
FROM: https://api.example.com/create
18:25:42.762 [Test worker @kotlinx.coroutines.test runner#134] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
18:25:42.764 [Test worker @kotlinx.coroutines.test runner#134] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
18:25:42.766 [Test worker @kotlinx.coroutines.test runner#138] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
18:25:42.766 [Test worker @kotlinx.coroutines.test runner#138] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
18:25:42.768 [Test worker @kotlinx.coroutines.test runner#142] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
18:25:42.875 [Test worker @kotlinx.coroutines.test runner#142] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
18:25:42.875 [Test worker @kotlinx.coroutines.test runner#142] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
18:25:42.980 [Test worker @kotlinx.coroutines.test runner#142] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
18:25:42.982 [Test worker @kotlinx.coroutines.test runner#142] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
18:25:43.088 [Test worker @kotlinx.coroutines.test runner#142] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
18:25:43.090 [Test worker @kotlinx.coroutines.test runner#142] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
18:25:43.197 [Test worker @kotlinx.coroutines.test runner#142] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
18:25:43.206 [Test worker @kotlinx.coroutines.test runner#152] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
18:25:43.207 [Test worker @kotlinx.coroutines.test runner#152] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Network error
18:25:43.209 [Test worker @kotlinx.coroutines.test runner#152] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
18:25:43.209 [Test worker @kotlinx.coroutines.test runner#152] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Network error
18:25:43.210 [Test worker @kotlinx.coroutines.test runner#152] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
18:25:43.211 [Test worker @kotlinx.coroutines.test runner#152] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
18:25:43.215 [Test worker @kotlinx.coroutines.test runner#160] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/notfound
METHOD: HttpMethod(value=GET)
18:25:43.216 [Test worker @kotlinx.coroutines.test runner#160] INFO io.ktor.client.HttpClient -- RESPONSE: 404 Not Found
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/notfound
18:25:43.221 [Test worker @kotlinx.coroutines.test runner#164] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
18:25:43.222 [Test worker @kotlinx.coroutines.test runner#164] INFO io.ktor.client.HttpClient -- RESPONSE: 429 Too Many Requests
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
18:25:43.223 [Test worker @kotlinx.coroutines.test runner#164] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
18:25:43.223 [Test worker @kotlinx.coroutines.test runner#164] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
