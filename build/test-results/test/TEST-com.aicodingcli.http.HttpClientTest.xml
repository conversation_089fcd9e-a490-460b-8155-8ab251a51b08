<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.http.HttpClientTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-06-17T11:29:08.916Z" hostname="zxnapdeMacBook-Pro.local" time="0.475">
  <properties/>
  <testcase name="should fail after max retries exceeded()" classname="com.aicodingcli.http.HttpClientTest" time="0.009"/>
  <testcase name="should make successful POST request with JSON body()" classname="com.aicodingcli.http.HttpClientTest" time="0.003"/>
  <testcase name="should add custom headers()" classname="com.aicodingcli.http.HttpClientTest" time="0.002"/>
  <testcase name="should make successful GET request()" classname="com.aicodingcli.http.HttpClientTest" time="0.001"/>
  <testcase name="should handle timeout()" classname="com.aicodingcli.http.HttpClientTest" time="0.441"/>
  <testcase name="should retry on network errors()" classname="com.aicodingcli.http.HttpClientTest" time="0.009"/>
  <testcase name="should handle HTTP error responses()" classname="com.aicodingcli.http.HttpClientTest" time="0.004"/>
  <testcase name="should handle rate limiting with retry after()" classname="com.aicodingcli.http.HttpClientTest" time="0.004"/>
  <system-out><![CDATA[19:29:08.919 [Test worker @kotlinx.coroutines.test runner#153] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
19:29:08.922 [Test worker @kotlinx.coroutines.test runner#153] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
19:29:08.923 [Test worker @kotlinx.coroutines.test runner#153] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
19:29:08.923 [Test worker @kotlinx.coroutines.test runner#153] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
19:29:08.924 [Test worker @kotlinx.coroutines.test runner#153] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
19:29:08.925 [Test worker @kotlinx.coroutines.test runner#153] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
19:29:08.927 [Test worker @kotlinx.coroutines.test runner#161] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/create
METHOD: HttpMethod(value=POST)
19:29:08.928 [Test worker @kotlinx.coroutines.test runner#161] INFO io.ktor.client.HttpClient -- RESPONSE: 201 Created
METHOD: HttpMethod(value=POST)
FROM: https://api.example.com/create
19:29:08.930 [Test worker @kotlinx.coroutines.test runner#165] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
19:29:08.930 [Test worker @kotlinx.coroutines.test runner#165] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
19:29:08.932 [Test worker @kotlinx.coroutines.test runner#169] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
19:29:08.932 [Test worker @kotlinx.coroutines.test runner#169] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
19:29:08.934 [Test worker @kotlinx.coroutines.test runner#173] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
19:29:09.046 [Test worker @kotlinx.coroutines.test runner#173] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
19:29:09.047 [Test worker @kotlinx.coroutines.test runner#173] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
19:29:09.159 [Test worker @kotlinx.coroutines.test runner#173] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
19:29:09.160 [Test worker @kotlinx.coroutines.test runner#173] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
19:29:09.267 [Test worker @kotlinx.coroutines.test runner#173] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
19:29:09.269 [Test worker @kotlinx.coroutines.test runner#173] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
19:29:09.372 [Test worker @kotlinx.coroutines.test runner#173] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
19:29:09.378 [Test worker @kotlinx.coroutines.test runner#183] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
19:29:09.379 [Test worker @kotlinx.coroutines.test runner#183] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Network error
19:29:09.380 [Test worker @kotlinx.coroutines.test runner#183] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
19:29:09.381 [Test worker @kotlinx.coroutines.test runner#183] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Network error
19:29:09.382 [Test worker @kotlinx.coroutines.test runner#183] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
19:29:09.382 [Test worker @kotlinx.coroutines.test runner#183] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
19:29:09.385 [Test worker @kotlinx.coroutines.test runner#191] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/notfound
METHOD: HttpMethod(value=GET)
19:29:09.386 [Test worker @kotlinx.coroutines.test runner#191] INFO io.ktor.client.HttpClient -- RESPONSE: 404 Not Found
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/notfound
19:29:09.389 [Test worker @kotlinx.coroutines.test runner#195] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
19:29:09.390 [Test worker @kotlinx.coroutines.test runner#195] INFO io.ktor.client.HttpClient -- RESPONSE: 429 Too Many Requests
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
19:29:09.390 [Test worker @kotlinx.coroutines.test runner#195] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
19:29:09.391 [Test worker @kotlinx.coroutines.test runner#195] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
