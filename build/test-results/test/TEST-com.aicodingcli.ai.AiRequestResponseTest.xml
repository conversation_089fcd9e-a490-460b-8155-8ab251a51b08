<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.ai.AiRequestResponseTest" tests="5" skipped="0" failures="0" errors="0" timestamp="2025-06-17T10:25:41.998Z" hostname="zxnapdeMacBook-Pro.local" time="0.516">
  <properties/>
  <testcase name="should create AI response with content()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.001"/>
  <testcase name="should create AI request with required fields()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.003"/>
  <testcase name="should handle streaming response()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.509"/>
  <testcase name="should handle conversation with multiple messages()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.0"/>
  <testcase name="should validate message content is not empty()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.001"/>
  <system-out><![CDATA[18:25:42.276 [Test worker @kotlinx.coroutines.test runner#2] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiService name=#1
18:25:42.506 [Test worker @kotlinx.coroutines.test runner#2] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for Flow name=child of #1#2
18:25:42.509 [Test worker @kotlinx.coroutines.test runner#2] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering kotlinx.coroutines.flow.FlowKt__BuildersKt$flowOf$$inlined$unsafeFlow$1@7f9e8421 on AiService(#1).streamChat(AiRequest(messages=[AiMessage(role=USER, content=Hello)], model=gpt-3.5-turbo, temperature=0.7, maxTokens=1000, stream=false), continuation {})
]]></system-out>
  <system-err><![CDATA[WARNING: A Java agent has been loaded dynamically (/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.bytebuddy/byte-buddy-agent/1.14.6/46e2545d7a97b6ccb195621650c5957279eb4812/byte-buddy-agent-1.14.6.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
]]></system-err>
</testsuite>
