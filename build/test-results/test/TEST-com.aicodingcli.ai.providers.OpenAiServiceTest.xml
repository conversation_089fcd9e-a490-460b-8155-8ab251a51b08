<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.ai.providers.OpenAiServiceTest" tests="7" skipped="0" failures="0" errors="0" timestamp="2025-06-17T10:38:33.160Z" hostname="zxnapdeMacBook-Pro.local" time="0.017">
  <properties/>
  <testcase name="should use custom base URL if provided()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.004"/>
  <testcase name="should test connection successfully()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.002"/>
  <testcase name="should fail connection test on error()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.001"/>
  <testcase name="should handle streaming chat request()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.001"/>
  <testcase name="should validate request before sending()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.001"/>
  <testcase name="should make successful chat request()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.004"/>
  <testcase name="should handle API error response()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.002"/>
  <system-out><![CDATA[18:38:33.161 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#29
18:38:33.162 [Test worker @kotlinx.coroutines.test runner#96] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #29#30
18:38:33.163 [Test worker @kotlinx.coroutines.test runner#96] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "chatcmpl-test",
    "object": "chat.completion",
    "created": **********,
    "model": "gpt-3.5-turbo",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Hi"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 1,
        "completion_tokens": 1,
        "total_tokens": 2
    }
}, headers={}) on <EMAIL>(https://custom.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
18:38:33.166 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#31
18:38:33.167 [Test worker @kotlinx.coroutines.test runner#102] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #31#32
18:38:33.167 [Test worker @kotlinx.coroutines.test runner#102] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={"object": "list", "data": [{"id": "gpt-3.5-turbo", "object": "model", "created": 1677610602, "owned_by": "openai"}]}, headers={Content-Type=application/json}) on <EMAIL>(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
18:38:33.169 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#33
18:38:33.170 [Test worker @kotlinx.coroutines.test runner#106] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #33#34
18:38:33.170 [Test worker @kotlinx.coroutines.test runner#106] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
18:38:33.170 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#35
18:38:33.171 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#36
18:38:33.172 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#37
18:38:33.174 [Test worker @kotlinx.coroutines.test runner#114] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #37#38
18:38:33.174 [Test worker @kotlinx.coroutines.test runner#114] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "chatcmpl-123",
    "object": "chat.completion",
    "created": **********,
    "model": "gpt-3.5-turbo",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Hello! How can I help you today?"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 9,
        "completion_tokens": 12,
        "total_tokens": 21
    }
}, headers={Content-Type=application/json}) on <EMAIL>(https://api.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello, AI!"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
18:38:33.176 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#39
18:38:33.177 [Test worker @kotlinx.coroutines.test runner#120] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #39#40
18:38:33.177 [Test worker @kotlinx.coroutines.test runner#120] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
