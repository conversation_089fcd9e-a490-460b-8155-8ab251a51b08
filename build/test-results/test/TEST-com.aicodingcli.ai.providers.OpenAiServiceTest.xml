<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.ai.providers.OpenAiServiceTest" tests="7" skipped="0" failures="0" errors="0" timestamp="2025-06-17T10:48:01.342Z" hostname="zxnapdeMacBook-Pro.local" time="0.026">
  <properties/>
  <testcase name="should use custom base URL if provided()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.01"/>
  <testcase name="should test connection successfully()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.004"/>
  <testcase name="should fail connection test on error()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.002"/>
  <testcase name="should handle streaming chat request()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.001"/>
  <testcase name="should validate request before sending()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.001"/>
  <testcase name="should make successful chat request()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.003"/>
  <testcase name="should handle API error response()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.003"/>
  <system-out><![CDATA[18:48:01.343 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#29
18:48:01.345 [Test worker @kotlinx.coroutines.test runner#97] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #29#30
18:48:01.347 [Test worker @kotlinx.coroutines.test runner#97] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "chatcmpl-test",
    "object": "chat.completion",
    "created": **********,
    "model": "gpt-3.5-turbo",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Hi"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 1,
        "completion_tokens": 1,
        "total_tokens": 2
    }
}, headers={}) on <EMAIL>(https://custom.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
18:48:01.353 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#31
18:48:01.354 [Test worker @kotlinx.coroutines.test runner#103] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #31#32
18:48:01.355 [Test worker @kotlinx.coroutines.test runner#103] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={"object": "list", "data": [{"id": "gpt-3.5-turbo", "object": "model", "created": 1677610602, "owned_by": "openai"}]}, headers={Content-Type=application/json}) on <EMAIL>(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
18:48:01.357 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#33
18:48:01.358 [Test worker @kotlinx.coroutines.test runner#107] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #33#34
18:48:01.359 [Test worker @kotlinx.coroutines.test runner#107] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
18:48:01.359 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#35
18:48:01.361 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#36
18:48:01.362 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#37
18:48:01.363 [Test worker @kotlinx.coroutines.test runner#115] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #37#38
18:48:01.363 [Test worker @kotlinx.coroutines.test runner#115] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "chatcmpl-123",
    "object": "chat.completion",
    "created": **********,
    "model": "gpt-3.5-turbo",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Hello! How can I help you today?"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 9,
        "completion_tokens": 12,
        "total_tokens": 21
    }
}, headers={Content-Type=application/json}) on <EMAIL>(https://api.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello, AI!"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
18:48:01.366 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#39
18:48:01.367 [Test worker @kotlinx.coroutines.test runner#121] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #39#40
18:48:01.367 [Test worker @kotlinx.coroutines.test runner#121] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
