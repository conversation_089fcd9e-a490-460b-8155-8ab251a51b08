<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.ai.providers.OpenAiServiceTest" tests="7" skipped="0" failures="0" errors="0" timestamp="2025-06-17T09:48:02.358Z" hostname="zxnapdeMacBook-Pro.local" time="0.017">
  <properties/>
  <testcase name="should use custom base URL if provided()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.005"/>
  <testcase name="should test connection successfully()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.003"/>
  <testcase name="should fail connection test on error()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.002"/>
  <testcase name="should handle streaming chat request()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.001"/>
  <testcase name="should validate request before sending()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.0"/>
  <testcase name="should make successful chat request()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.003"/>
  <testcase name="should handle API error response()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.002"/>
  <system-out><![CDATA[17:48:02.358 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#15
17:48:02.360 [Test worker @kotlinx.coroutines.test runner#48] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #15#16
17:48:02.361 [Test worker @kotlinx.coroutines.test runner#48] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "chatcmpl-test",
    "object": "chat.completion",
    "created": **********,
    "model": "gpt-3.5-turbo",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Hi"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 1,
        "completion_tokens": 1,
        "total_tokens": 2
    }
}, headers={}) on <EMAIL>(https://custom.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
17:48:02.364 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#17
17:48:02.365 [Test worker @kotlinx.coroutines.test runner#54] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #17#18
17:48:02.365 [Test worker @kotlinx.coroutines.test runner#54] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={"object": "list", "data": [{"id": "gpt-3.5-turbo", "object": "model", "created": 1677610602, "owned_by": "openai"}]}, headers={Content-Type=application/json}) on <EMAIL>(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
17:48:02.367 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#19
17:48:02.368 [Test worker @kotlinx.coroutines.test runner#58] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #19#20
17:48:02.368 [Test worker @kotlinx.coroutines.test runner#58] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
17:48:02.369 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#21
17:48:02.370 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#22
17:48:02.370 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#23
17:48:02.372 [Test worker @kotlinx.coroutines.test runner#66] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #23#24
17:48:02.372 [Test worker @kotlinx.coroutines.test runner#66] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "chatcmpl-123",
    "object": "chat.completion",
    "created": **********,
    "model": "gpt-3.5-turbo",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Hello! How can I help you today?"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 9,
        "completion_tokens": 12,
        "total_tokens": 21
    }
}, headers={Content-Type=application/json}) on <EMAIL>(https://api.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello, AI!"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
17:48:02.374 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#25
17:48:02.375 [Test worker @kotlinx.coroutines.test runner#72] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #25#26
17:48:02.375 [Test worker @kotlinx.coroutines.test runner#72] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
