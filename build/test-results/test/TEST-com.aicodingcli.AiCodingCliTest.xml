<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.AiCodingCliTest" tests="12" skipped="0" failures="0" errors="0" timestamp="2025-06-17T11:29:07.890Z" hostname="zxnapdeMacBook-Pro.local" time="0.452">
  <properties/>
  <testcase name="should show error for unknown command()" classname="com.aicodingcli.AiCodingCliTest" time="0.01"/>
  <testcase name="should handle config provider with no arguments()" classname="com.aicodingcli.AiCodingCliTest" time="0.007"/>
  <testcase name="should print help when no arguments provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.0"/>
  <testcase name="should handle config get with no arguments()" classname="com.aicodingcli.AiCodingCliTest" time="0.0"/>
  <testcase name="should handle stream parameter correctly()" classname="com.aicodingcli.AiCodingCliTest" time="0.401"/>
  <testcase name="should handle config list command()" classname="com.aicodingcli.AiCodingCliTest" time="0.005"/>
  <testcase name="should print help when --help argument is provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <testcase name="should handle invalid config subcommand()" classname="com.aicodingcli.AiCodingCliTest" time="0.0"/>
  <testcase name="should handle config set with insufficient arguments()" classname="com.aicodingcli.AiCodingCliTest" time="0.0"/>
  <testcase name="should handle model parameter correctly()" classname="com.aicodingcli.AiCodingCliTest" time="0.022"/>
  <testcase name="should show config help when config command has no arguments()" classname="com.aicodingcli.AiCodingCliTest" time="0.0"/>
  <testcase name="should print version when --version argument is provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
