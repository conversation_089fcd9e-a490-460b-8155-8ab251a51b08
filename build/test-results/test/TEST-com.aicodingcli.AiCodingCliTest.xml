<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.AiCodingCliTest" tests="11" skipped="0" failures="0" errors="0" timestamp="2025-06-17T10:48:00.480Z" hostname="zxnapdeMacBook-Pro.local" time="0.261">
  <properties/>
  <testcase name="should show error for unknown command()" classname="com.aicodingcli.AiCodingCliTest" time="0.01"/>
  <testcase name="should handle config provider with no arguments()" classname="com.aicodingcli.AiCodingCliTest" time="0.008"/>
  <testcase name="should print help when no arguments provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <testcase name="should handle config get with no arguments()" classname="com.aicodingcli.AiCodingCliTest" time="0.0"/>
  <testcase name="should handle config list command()" classname="com.aicodingcli.AiCodingCliTest" time="0.024"/>
  <testcase name="should print help when --help argument is provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <testcase name="should handle invalid config subcommand()" classname="com.aicodingcli.AiCodingCliTest" time="0.0"/>
  <testcase name="should handle config set with insufficient arguments()" classname="com.aicodingcli.AiCodingCliTest" time="0.0"/>
  <testcase name="should handle model parameter correctly()" classname="com.aicodingcli.AiCodingCliTest" time="0.212"/>
  <testcase name="should show config help when config command has no arguments()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <testcase name="should print version when --version argument is provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
