<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.AiCodingCliTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-06-17T08:50:41.072Z" hostname="zxnapdeMacBook-Pro.local" time="0.012">
  <properties/>
  <testcase name="should show error for unknown command()" classname="com.aicodingcli.AiCodingCliTest" time="0.01"/>
  <testcase name="should print help when no arguments provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <testcase name="should print help when --help argument is provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.0"/>
  <testcase name="should print version when --version argument is provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
