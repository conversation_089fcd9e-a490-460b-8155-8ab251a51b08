<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.config.ConfigManagerTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-06-17T08:50:41.759Z" hostname="zxnapdeMacBook-Pro.local" time="0.02">
  <properties/>
  <testcase name="should load existing configuration file()" classname="com.aicodingcli.config.ConfigManagerTest" time="0.011"/>
  <testcase name="should get current provider configuration()" classname="com.aicodingcli.config.ConfigManagerTest" time="0.001"/>
  <testcase name="should save configuration to file()" classname="com.aicodingcli.config.ConfigManagerTest" time="0.001"/>
  <testcase name="should create default configuration file if not exists()" classname="com.aicodingcli.config.ConfigManagerTest" time="0.001"/>
  <testcase name="should update provider configuration()" classname="com.aicodingcli.config.ConfigManagerTest" time="0.002"/>
  <testcase name="should switch default provider()" classname="com.aicodingcli.config.ConfigManagerTest" time="0.002"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
