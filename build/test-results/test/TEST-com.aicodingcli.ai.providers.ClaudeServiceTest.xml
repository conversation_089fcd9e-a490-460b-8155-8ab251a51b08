<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.ai.providers.ClaudeServiceTest" tests="7" skipped="0" failures="0" errors="0" timestamp="2025-06-17T10:25:42.540Z" hostname="zxnapdeMacBook-Pro.local" time="0.104">
  <properties/>
  <testcase name="should use custom base URL if provided()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.083"/>
  <testcase name="should test connection successfully()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.003"/>
  <testcase name="should fail connection test on error()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.005"/>
  <testcase name="should handle streaming chat request()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.002"/>
  <testcase name="should validate request before sending()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.001"/>
  <testcase name="should make successful chat request()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.004"/>
  <testcase name="should handle API error response()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.003"/>
  <system-out><![CDATA[18:25:42.541 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#3
18:25:42.562 [Test worker] WARN io.mockk.proxy.jvm.transformation.InliningClassTransformer -- Failed to transform class java/lang/Object
java.lang.IllegalArgumentException: Java 23 (67) is not supported by the current version of Byte Buddy which officially supports Java 21 (65) - update Byte Buddy or set net.bytebuddy.experimental as a VM property
	at net.bytebuddy.utility.OpenedClassReader.of(OpenedClassReader.java:96)
	at net.bytebuddy.dynamic.scaffold.TypeWriter$Default$ForInlining.create(TypeWriter.java:4011)
	at net.bytebuddy.dynamic.scaffold.TypeWriter$Default.make(TypeWriter.java:2224)
	at net.bytebuddy.dynamic.DynamicType$Builder$AbstractBase$UsingTypeWriter.make(DynamicType.java:4055)
	at net.bytebuddy.dynamic.DynamicType$Builder$AbstractBase.make(DynamicType.java:3739)
	at io.mockk.proxy.jvm.transformation.InliningClassTransformer.transform(InliningClassTransformer.kt:81)
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:242)
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:610)
	at java.instrument/sun.instrument.InstrumentationImpl.retransformClasses0(Native Method)
	at java.instrument/sun.instrument.InstrumentationImpl.retransformClasses(InstrumentationImpl.java:225)
	at io.mockk.proxy.jvm.transformation.JvmInlineInstrumentation.retransform(JvmInlineInstrumentation.kt:28)
	at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation$execute$1.invoke(RetransformInlineInstrumentation.kt:19)
	at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation$execute$1.invoke(RetransformInlineInstrumentation.kt:16)
	at io.mockk.proxy.common.transformation.ClassTransformationSpecMap.applyTransformation(ClassTransformationSpecMap.kt:41)
	at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation.execute(RetransformInlineInstrumentation.kt:16)
	at io.mockk.proxy.jvm.ProxyMaker.inline(ProxyMaker.kt:90)
	at io.mockk.proxy.jvm.ProxyMaker.proxy(ProxyMaker.kt:34)
	at io.mockk.impl.instantiation.JvmMockFactory.newProxy(JvmMockFactory.kt:34)
	at io.mockk.impl.instantiation.AbstractMockFactory.newProxy$default(AbstractMockFactory.kt:24)
	at io.mockk.impl.instantiation.AbstractMockFactory.mockk(AbstractMockFactory.kt:59)
	at com.aicodingcli.ai.providers.ClaudeServiceTest.setUp(ClaudeServiceTest.kt:295)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptLifecycleMethod(TimeoutExtension.java:128)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptBeforeEachMethod(TimeoutExtension.java:78)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeMethodInExtensionContext(ClassBasedTestDescriptor.java:521)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$synthesizeBeforeEachMethodAdapter$23(ClassBasedTestDescriptor.java:506)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeBeforeEachMethods$3(TestMethodTestDescriptor.java:175)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeBeforeMethodsOrCallbacksUntilExceptionOccurs$6(TestMethodTestDescriptor.java:203)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeBeforeMethodsOrCallbacksUntilExceptionOccurs(TestMethodTestDescriptor.java:203)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeBeforeEachMethods(TestMethodTestDescriptor.java:172)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:135)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:122)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:72)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
18:25:42.608 [Test worker @kotlinx.coroutines.test runner#20] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #3#4
18:25:42.616 [Test worker @kotlinx.coroutines.test runner#20] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "msg_test",
    "type": "message",
    "role": "assistant",
    "content": [
        {
            "type": "text",
            "text": "Hi"
        }
    ],
    "model": "claude-3-sonnet-20240229",
    "stop_reason": "end_turn",
    "stop_sequence": null,
    "usage": {
        "input_tokens": 1,
        "output_tokens": 1
    }
}, headers={}) on <EMAIL>(https://custom.anthropic.com/v1/messages, {"model":"claude-3-sonnet-20240229","messages":[{"role":"user","content":"Hello"}],"max_tokens":1000,"temperature":0.7}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
18:25:42.626 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#5
18:25:42.627 [Test worker @kotlinx.coroutines.test runner#26] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #5#6
18:25:42.628 [Test worker @kotlinx.coroutines.test runner#26] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "msg_test",
    "type": "message",
    "role": "assistant",
    "content": [
        {
            "type": "text",
            "text": "Connection test successful"
        }
    ],
    "model": "claude-3-sonnet-20240229",
    "stop_reason": "end_turn",
    "stop_sequence": null,
    "usage": {
        "input_tokens": 5,
        "output_tokens": 5
    }
}, headers={Content-Type=application/json}) on <EMAIL>(https://api.anthropic.com/v1/messages, {"model":"claude-3-sonnet-20240229","messages":[{"role":"user","content":"Test connection"}],"max_tokens":10}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
18:25:42.629 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#7
18:25:42.631 [Test worker @kotlinx.coroutines.test runner#30] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #7#8
18:25:42.633 [Test worker @kotlinx.coroutines.test runner#30] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.anthropic.com/v1/messages, {"model":"claude-3-sonnet-20240229","messages":[{"role":"user","content":"Test connection"}],"max_tokens":10}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
18:25:42.634 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#9
18:25:42.636 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#10
18:25:42.637 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#11
18:25:42.639 [Test worker @kotlinx.coroutines.test runner#38] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #11#12
18:25:42.639 [Test worker @kotlinx.coroutines.test runner#38] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "msg_01XFDUDYJgAACzvnptvVoYEL",
    "type": "message",
    "role": "assistant",
    "content": [
        {
            "type": "text",
            "text": "Hello! How can I help you today?"
        }
    ],
    "model": "claude-3-sonnet-20240229",
    "stop_reason": "end_turn",
    "stop_sequence": null,
    "usage": {
        "input_tokens": 10,
        "output_tokens": 15
    }
}, headers={Content-Type=application/json}) on <EMAIL>(https://api.anthropic.com/v1/messages, {"model":"claude-3-sonnet-20240229","messages":[{"role":"user","content":"Hello, Claude!"}],"max_tokens":1000,"temperature":0.7}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
18:25:42.642 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#13
18:25:42.643 [Test worker @kotlinx.coroutines.test runner#44] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #13#14
18:25:42.643 [Test worker @kotlinx.coroutines.test runner#44] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.anthropic.com/v1/messages, {"model":"claude-3-sonnet-20240229","messages":[{"role":"user","content":"Hello"}],"max_tokens":1000,"temperature":0.7}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
